#!/usr/bin/env python3
"""
Complete end-to-end test for the bulk user import functionality.
"""

import os
import sys
import tempfile
import requests
from io import BytesIO

# Add the app directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_complete_import_flow():
    """Test the complete import flow with authentication."""
    
    session = requests.Session()
    base_url = "http://127.0.0.1:5002"
    
    try:
        print("🧪 Testing Complete Import Flow...")
        
        # Step 1: Login as admin
        print("1. Logging in as admin...")
        login_response = session.post(f"{base_url}/auth/login", data={
            'email': '<EMAIL>',
            'password': 'Admin123!',
            'csrf_token': get_csrf_token(session, f"{base_url}/auth/login")
        })
        
        if login_response.status_code == 302 and '/admin' in login_response.headers.get('Location', ''):
            print("✅ Successfully logged in as admin")
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            return
        
        # Step 2: Access the bulk import form
        print("2. Accessing bulk import form...")
        form_response = session.get(f"{base_url}/admin/users/bulk-import/form")
        
        if form_response.status_code == 200:
            print("✅ Bulk import form loaded successfully")
        else:
            print(f"❌ Form access failed: {form_response.status_code}")
            return
        
        # Step 3: Create test CSV data
        print("3. Creating test CSV data...")
        csv_content = """name,email,role,first_name,middle_name,last_name,legal_name,job_title,emp_type,enterprise_id,job_code,job_code_track_level,manager_level,business_unit_code,hire_date,EMP_STATUS
Test Import User,<EMAIL>,User,Test,Import,User,Test Import User,Software Engineer,Full-time,EMP999,SE01,L2,M1,OPS,2024-01-15,active"""
        
        # Step 4: Submit the import
        print("4. Submitting bulk import...")
        
        # Get CSRF token from the form
        csrf_token = get_csrf_token(session, f"{base_url}/admin/users/bulk-import/form")
        
        # Create file data
        files = {
            'file': ('test_import.csv', BytesIO(csv_content.encode('utf-8')), 'text/csv')
        }
        
        data = {
            'csrf_token': csrf_token,
            'default_role': 'User',
            'handle_duplicates': 'skip',
            'default_password': 'TestPassword123!',
            'send_welcome_email': 'no'
        }
        
        import_response = session.post(
            f"{base_url}/admin/users/drawer/bulk-import",
            files=files,
            data=data,
            headers={'X-Requested-With': 'XMLHttpRequest'}
        )
        
        print(f"Import response status: {import_response.status_code}")
        
        if import_response.status_code == 200:
            try:
                result = import_response.json()
                print(f"Import result: {result}")
                
                if result.get('success'):
                    print("✅ Import completed successfully!")
                    print(f"Created: {result.get('details', {}).get('created', 0)}")
                    print(f"Updated: {result.get('details', {}).get('updated', 0)}")
                    print(f"Skipped: {result.get('details', {}).get('skipped', 0)}")
                    print(f"Errors: {result.get('details', {}).get('errors', 0)}")
                else:
                    print(f"❌ Import failed: {result.get('message', 'Unknown error')}")
                    
            except Exception as e:
                print(f"❌ Error parsing import response: {e}")
                print(f"Response text: {import_response.text[:500]}")
        else:
            print(f"❌ Import request failed: {import_response.status_code}")
            print(f"Response: {import_response.text[:500]}")
        
        # Step 5: Verify user was created
        print("5. Verifying user creation...")
        verify_user_creation()
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to the server. Make sure it's running on port 5002")
    except Exception as e:
        print(f"❌ Error during testing: {e}")

def get_csrf_token(session, url):
    """Extract CSRF token from a page."""
    try:
        response = session.get(url)
        if response.status_code == 200:
            # Look for CSRF token in the response
            import re
            match = re.search(r'name="csrf_token"[^>]*value="([^"]*)"', response.text)
            if match:
                return match.group(1)
            
            # Alternative: look for meta tag
            match = re.search(r'<meta name="csrf-token" content="([^"]*)"', response.text)
            if match:
                return match.group(1)
        
        return None
    except Exception:
        return None

def verify_user_creation():
    """Verify that the test user was created."""
    from app import create_app, db
    from app.models import User
    
    app = create_app()
    with app.app_context():
        test_user = User.query.filter_by(email='<EMAIL>').first()
        
        if test_user:
            print("✅ Test user was created successfully!")
            print(f"   Name: {test_user.name}")
            print(f"   Email: {test_user.email}")
            print(f"   Role: {test_user.role}")
            
            if test_user.employee_detail:
                print(f"   Employee Detail: {test_user.employee_detail.first_name} {test_user.employee_detail.last_name}")
                print(f"   Job Title: {test_user.employee_detail.job_title}")
                print(f"   Enterprise ID: {test_user.employee_detail.enterprise_id}")
                print(f"   Job Code: {test_user.employee_detail.job_code}")
                print(f"   Manager Level: {test_user.employee_detail.manager_level}")
            
            # Clean up
            if test_user.employee_detail:
                db.session.delete(test_user.employee_detail)
            db.session.delete(test_user)
            db.session.commit()
            print("🧹 Cleaned up test user")
        else:
            print("❌ Test user was not created")

if __name__ == '__main__':
    test_complete_import_flow()
    print("\n✅ Complete import test finished!")
